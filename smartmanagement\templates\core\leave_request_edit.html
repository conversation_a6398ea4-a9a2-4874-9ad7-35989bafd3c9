{% extends 'base.html' %}

{% block title %}Edit Leave Request - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
    }
    

    
    .half-day-controls {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
        display: none;
    }
    
    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .text-danger {
        font-size: 0.875rem;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-edit me-3"></i>Edit Leave Request
                        </h1>
                        <p class="lead mb-0">Update your leave request details</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_request_detail' leave_request.pk %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <div class="form-header">
                    <h2><i class="fas fa-calendar-edit me-2"></i>Edit Leave Request</h2>
                    <p class="mb-0">Update your {{ leave_request.leave_type.name }} request</p>
                </div>

                <!-- Holiday Information -->
                <div class="p-4">
                    <div class="leave-balance-info" id="holidayInfo" style="display: none;">
                        <h5><i class="fas fa-calendar-alt me-2"></i>Holidays in Selected Period</h5>
                        <div id="holidayList">
                            <!-- Holidays will be loaded here via AJAX -->
                        </div>
                        <small class="text-muted">Note: Holidays are automatically excluded from leave calculations</small>
                    </div>
                    
                    <!-- Leave Request Form -->
                    <form method="post" id="leaveRequestForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.leave_type.id_for_label }}" class="form-label">
                                        Leave Type <span class="text-danger">*</span>
                                    </label>
                                    {{ form.leave_type }}
                                    {% if form.leave_type.errors %}
                                        <div class="text-danger mt-1">{{ form.leave_type.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">
                                        Emergency Contact
                                    </label>
                                    {{ form.emergency_contact }}
                                    {% if form.emergency_contact.errors %}
                                        <div class="text-danger mt-1">{{ form.emergency_contact.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">
                                        Start Date <span class="text-danger">*</span>
                                    </label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger mt-1">{{ form.start_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                        End Date <span class="text-danger">*</span>
                                    </label>
                                    {{ form.end_date }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger mt-1">{{ form.end_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Half Day Option -->
                        <div class="form-group">
                            <div class="form-check">
                                {{ form.is_half_day }}
                                <label class="form-check-label" for="{{ form.is_half_day.id_for_label }}">
                                    This is a half-day leave
                                </label>
                            </div>
                            
                            <div class="half-day-controls" id="halfDayControls">
                                <label for="{{ form.half_day_period.id_for_label }}" class="form-label">
                                    Half Day Period
                                </label>
                                {{ form.half_day_period }}
                                {% if form.half_day_period.errors %}
                                    <div class="text-danger mt-1">{{ form.half_day_period.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.reason.id_for_label }}" class="form-label">
                                Reason for Leave <span class="text-danger">*</span>
                            </label>
                            {{ form.reason }}
                            {% if form.reason.errors %}
                                <div class="text-danger mt-1">{{ form.reason.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Leave Days Calculation -->
                        <div class="alert alert-info" id="leaveDaysInfo" style="display: none;">
                            <i class="fas fa-calculator me-2"></i>
                            <span id="leaveDaysText">Total leave days: 0</span>
                        </div>
                        
                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <a href="{% url 'core:leave_request_detail' leave_request.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Leave Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const halfDayCheck = document.getElementById('halfDayCheck');
    const halfDayControls = document.getElementById('halfDayControls');
    const startDateInput = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateInput = document.getElementById('{{ form.end_date.id_for_label }}');
    const leaveTypeSelect = document.getElementById('{{ form.leave_type.id_for_label }}');
    const leaveDaysInfo = document.getElementById('leaveDaysInfo');
    const leaveDaysText = document.getElementById('leaveDaysText');
    
    // Show/hide half day controls
    function toggleHalfDayControls() {
        if (halfDayCheck && halfDayCheck.checked) {
            halfDayControls.style.display = 'block';
            // Set end date same as start date for half day
            if (startDateInput.value) {
                endDateInput.value = startDateInput.value;
            }
        } else {
            halfDayControls.style.display = 'none';
        }
    }
    
    // Initialize half day controls
    if (halfDayCheck) {
        toggleHalfDayControls();
        halfDayCheck.addEventListener('change', toggleHalfDayControls);
    }
    
    // Calculate leave days
    function calculateLeaveDays() {
        if (startDateInput.value && endDateInput.value && leaveTypeSelect.value) {
            const formData = new FormData();
            formData.append('start_date', startDateInput.value);
            formData.append('end_date', endDateInput.value);
            formData.append('leave_type', leaveTypeSelect.value);
            formData.append('is_half_day', halfDayCheck ? halfDayCheck.checked : false);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
            
            fetch('{% url "core:calculate_leave_days" %}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    leaveDaysText.textContent = `Total leave days: ${data.total_days}`;
                    leaveDaysInfo.style.display = 'block';
                } else {
                    leaveDaysInfo.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error calculating leave days:', error);
                leaveDaysInfo.style.display = 'none';
            });
        } else {
            leaveDaysInfo.style.display = 'none';
        }
    }
    

    
    // Load holidays function
    function loadHolidays() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;

        if (startDate && endDate) {
            fetch(`{% url "core:get_holidays" %}?start_date=${startDate}&end_date=${endDate}`)
                .then(response => response.json())
                .then(data => {
                    const holidayInfo = document.getElementById('holidayInfo');
                    const holidayList = document.getElementById('holidayList');

                    if (data.success && data.holidays.length > 0) {
                        holidayInfo.style.display = 'block';
                        holidayList.innerHTML = data.holidays.map(holiday => `
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <div>
                                    <strong>${holiday.name}</strong>
                                    <small class="text-muted d-block">${holiday.formatted_date} (${holiday.day_name})</small>
                                    ${holiday.description ? `<small class="text-muted">${holiday.description}</small>` : ''}
                                </div>
                                <span class="badge ${holiday.is_optional ? 'bg-warning' : 'bg-success'} text-dark">
                                    ${holiday.is_optional ? 'Optional' : 'Mandatory'}
                                </span>
                            </div>
                        `).join('');
                    } else {
                        holidayInfo.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading holidays:', error);
                    document.getElementById('holidayInfo').style.display = 'none';
                });
        } else {
            document.getElementById('holidayInfo').style.display = 'none';
        }
    }

    // Event listeners
    if (startDateInput) startDateInput.addEventListener('change', function() {
        calculateLeaveDays();
        loadHolidays();
    });
    if (endDateInput) endDateInput.addEventListener('change', function() {
        calculateLeaveDays();
        loadHolidays();
    });
    if (leaveTypeSelect) leaveTypeSelect.addEventListener('change', calculateLeaveDays);
    if (halfDayCheck) halfDayCheck.addEventListener('change', function() {
        calculateLeaveDays();
        loadHolidays();
    });

    // Load initial data
    loadLeaveBalances();
    calculateLeaveDays();
    loadHolidays();
});
</script>
{% endblock %}
